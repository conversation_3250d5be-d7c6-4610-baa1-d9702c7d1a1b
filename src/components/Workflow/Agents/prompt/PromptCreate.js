import React, { useState, useEffect } from 'react';
import { Form, Input, Button, message, Modal, notification, Select, Switch, InputNumber, Row, Col } from 'antd';
import { useNavigate } from 'react-router-dom';
import './Prompt.css'; 
import { 
  PROMPTS_NAME, PROMPT_ENDPOINT, AGENT_ENDPOINT,
  LLM_CHAT_ENDPOINT, LLM_ENDPOINT
} from '../../../Configs/Config';
import { createData, fetchBulk, fetchData } from '../../../Routers/Router';
import { getConstantTypesOptions } from '../Utils/utils';
import SidebarList from '../../../SidebarList';
import LoadingSpinner from '../../../utils/LoadingSpinner'; 
import { generatePromptDescription } from './Utils';
import { useSelector } from 'react-redux';
import ParametersForm from './ParametersForm';
const { Option } = Select;
const { TextArea } = Input;

const CreatePrompt = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false); 
  const [taskTypeOptions, setTaskTypeOptions] = useState([]);
  const [cotTemplateOptions, setCotTemplateOptions] = useState([]);
  const [promptData, setPromptData] = useState(null); 
  const [llmData, setLlmData] = useState([]); 
  const [selected_llm, setSelectedLlm] = useState(null); 
  const [isActive, setIsActive] = useState(true);
  const navigate = useNavigate();
  const promptName = 'co-star格式提示词生成器'; 
  const { user_info } = useSelector((state) => state.user);

  useEffect(() => {
    const fetchWorkflows = async () => {
      try {
        const endpoint_api = `${LLM_ENDPOINT}/bulk?username=${user_info.username}`;
        const result = await fetchBulk(endpoint_api);
        const llm_data = result.data;
        setLlmData(llm_data);
  
        const prompt_endpoint = `${PROMPT_ENDPOINT}/query_by_name/${promptName}`;
        const prompt_result = await fetchData(prompt_endpoint);
        setPromptData(prompt_result);
      } catch (error) {
        console.error('Failed to fetch workflows or prompt:', error);
      }
    };
  
    fetchWorkflows();
    
    const fetchAgentTypeOptions = async () => {
      const task_endpoint_api = `${AGENT_ENDPOINT}/task_types`;
      const task_options = await getConstantTypesOptions(task_endpoint_api);
      setTaskTypeOptions(task_options);
  
      const cot_endpoint_api = `${PROMPT_ENDPOINT}/cots/`;
      const cot_options = await fetchData(cot_endpoint_api);
      setCotTemplateOptions(cot_options.data);
    };
  
    fetchAgentTypeOptions();
  }, [form]);
  
  // Separate useEffect to update form when cotTemplateOptions changes
  useEffect(() => {
    if (promptData && cotTemplateOptions.length > 0) {
      form.setFieldsValue({
        cot: cotTemplateOptions[0]?.value || "Let's think step by step",
        system_message: "You are a helpful assistant",
        parameters: [],
        output_parameters: [], // Initialize output_parameters as an empty array
      });
    }
  }, [cotTemplateOptions, promptData, form]);
  

  const handleGeneratePrompt = () => {
    generatePromptDescription({
      apiEndpoint: LLM_CHAT_ENDPOINT,
      prompts: promptData ? [promptData] : [], 
      selectedPrompt: promptName,
      llm_model_name: selected_llm,
      form,
      createData,
      setLoading
    });
  };

  const onFinish = (values) => {
    Modal.confirm({
      title: '确认保存',
      content: '您确定要保存这些更改吗？',
      onOk: async () => {
        setLoading(true); 
        try {
          const endpoint_api = `${PROMPT_ENDPOINT}/`;
          await createData(endpoint_api, values);
          message.success('Prompt created successfully');
          form.resetFields();
          navigate(`${PROMPTS_NAME}`);
        } catch (error) {
          notification.error({
            message: '创建失败',
            description: 'Failed to create Prompt. Please try again.',
            duration: 5,
          });
        } finally {
          setLoading(false); 
        }
      },
    });
  };

  return (
    <div className="page-container">
      {loading && <LoadingSpinner />} 

      <div className="header-container">
        <h1 className="page-title-prompt">创建新提示词</h1>
        <SidebarList /> 
      </div>
      <div className="form-container-create">
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{
            parameters: [],
            output_parameters: [], // Initialize output_parameters as an empty array
            is_active: true,
            prompt_type: "正向提示词",
            task_type: "大模型"
          }}
        >
          <Row gutter={24}>
            <Col span={24}>
              <Form.Item
                name="description"
                label="提示词描述"
                rules={[{ required: true, message: '请输入提示描述!' }]}
                extra={
                  <>
                    描述任务场景，点击
                    <span style={{ fontWeight: 'bold', color: 'blue' }}>一键生成</span>自动生成提示词， 如：
                    <span style={{ fontWeight: 'bold', color: 'blue' }}>
                    我正在仔细阅读一篇重要的研究文章，试图解读作者的核心观点、研究方法和结论。我需要将这些文献的内容进行深入分析，并与现有的理论框架相结合，为我的研究课题提供支持和新的视角
                   </span>
                  </>
                }
              >
                <TextArea rows={4} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={3} align="middle">
            <Col span={3}>
              <Form.Item
                name="llm_model"
                label="选择LLM模型"
              >
                <Select
                  value={selected_llm}
                  onChange={(value) => setSelectedLlm(value)}
                  placeholder="选择一个LLM模型"
                >
                  {llmData.map((llm) => (
                    <Option key={llm.id} value={llm.name}>
                      {llm.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col span={2}>
              <Button
                type="primary"
                onClick={handleGeneratePrompt}
                style={{ marginLeft: '10px' ,marginTop: '15px'}}
              >
                一键生成
              </Button>
            </Col>
          </Row>

          <Form.Item
            name="name"
            label="提示词名称"
            rules={[{ required: true, message: '请输入提示名称!' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="system_message"
            label="系统角色"
            rules={[{ required: true, message: '请输入系统角色!' }]}
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="cot"
            label="链式思维模版"
            rules={[{ required: true, message: '请选择链式思维模版!' }]}
          >
            <Select>
              {cotTemplateOptions.map((cot_template) => (
                <Option key={cot_template.value} value={cot_template.value}>
                  {cot_template.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="context"
            label="上下文"
            rules={[{ required: true, message: '请输入上下文!' }]}
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="objective"
            label="目标"
            rules={[{ required: true, message: '请输入目标!' }]}
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="style"
            label="风格"
            rules={[{ required: false, message: '请输入风格!' }]}
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="tone"
            label="语气"
            rules={[{ required: false, message: '请输入语气!' }]}
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="audience"
            label="受众"
            rules={[{ required: false, message: '请输入受众!' }]}
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="response_format"
            label="回复格式"
            rules={[{ required: false, message: '请输入回复格式!' }]}
          >
            <TextArea rows={4} />
          </Form.Item>

          {/* 输入参数字段 */}
          <Form.Item
            name="parameters"
            label="输入参数"
            tooltip="添加提示词需要的输入参数，例如查询内容、大纲标题，大纲子标题等,这个参数来自于上一个llm 生成的结果，默认字段content"
          >
            <ParametersForm />
          </Form.Item>

          {/* 新增输出参数字段 */}
          <Form.Item
            name="output_parameters"
            label="输出参数"
            tooltip="添加提示词需要的输出格式参数，指定大模型输出的结构化格式"
          >
            <ParametersForm />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="是否激活"
            valuePropName="checked"
            initialValue={isActive}
          >
            <Switch checked={isActive} onChange={(checked) => setIsActive(checked)} />
          </Form.Item>

          <Form.Item
            name="task_type"
            label="任务类型"
            initialValue={"大模型"}
            rules={[{ required: true, message: '请选择任务类型!' }]}
          >
            <Select>
              {taskTypeOptions.map((task_type) => (
                <Option key={task_type.value} value={task_type.value}>
                  {task_type.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            shouldUpdate={(prevValues, currentValues) => prevValues.task_type !== currentValues.task_type}
            noStyle
          >
            {({ getFieldValue }) =>
              getFieldValue('task_type') === '反思' ? (
                <Form.Item
                  name="num_reflection"
                  label="反思次数"
                  initialValue={1}
                  rules={[{ required: true, message: '请输入反思次数' }]}
                >
                  <InputNumber min={0} max={10} step={1} />
                </Form.Item>
              ) : null
            }
          </Form.Item>

          <Form.Item
            name="prompt_type"
            label="提示词类型"
            rules={[{ required: true, message: '请选择提示词类型!' }]}
            initialValue="正向提示词"
          >
            <Select>
              <Option value="负向提示词">负向提示词</Option>
              <Option value="正向提示词">正向提示词</Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              创建提示
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default CreatePrompt;