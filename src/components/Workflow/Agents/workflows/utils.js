// getNodeTypes.js
import React from 'react';
import LLMNode from '../../AgentNode/LLMNode';
import InterruptionNode from '../../AgentNode/InterruptionNode';
import SearchNode from '../../AgentNode/SearchNode';
import ToolNode from '../../AgentNode/ToolNode';
import FileParseNode from '../../AgentNode/FileParseNode';
import ResourceNode from '../../AgentNode/ResourceNode'
import RouterNode from '../../AgentNode/RouterNode';
import StartNode from '../../AgentNode/StartNode';
import DeepResearchNode from '../../AgentNode/DeepResearchNode';
import {
  QUERY_UNDERSTAND_NAME,
  OUTLINE_NAME,
  POPULAR_SCIENCE_IMITATING_WRITING_NAME,
  START_NODE_NAME,
  AGENT_ROUTER_NAME,  
  TOOL_NODE_NAME,
  LLM_NAME,
  ANSWER_REASONING_NAME,
  KNOWLEDGE_BASE_LLM_NAME,
  MCP_RESOURCE_NAME,
  FILE_PARSE_NAME,
  DEEP_RESEARCH_NAME,
  INTERRUPTION_NODE_NAME
} from '../../../Configs/Config';

export const getNodeTypes = (
  llmOptions, 
  kbOptions, 
  positivePrompts, 
  negativePrompts, 
  isDebugMode,
  tools,               // <--- 新增：由父组件传递进来的 tools
  mcpServers,
  mcpResources,
  // onToolNodeReady,     // <--- 新增：告诉父组件某个ToolNode加载完成
) => {
  const reflection_prompt = positivePrompts.filter(p => p.task_type === '反思');
  const filterPositivePrompts = positivePrompts.filter(p => p.task_type !== '反思');
  return ({
    [QUERY_UNDERSTAND_NAME]: (props) => (
      <LLMNode
        {...props}
        llm_options={llmOptions}
        name={QUERY_UNDERSTAND_NAME}
        positive_prompt_options={filterPositivePrompts}
        agent_type={QUERY_UNDERSTAND_NAME}
        negative_prompt_options={negativePrompts}
        isDebugMode={isDebugMode}
      />
    ),
    [OUTLINE_NAME]: (props) => (
      <LLMNode
        {...props}
        llm_options={llmOptions}
        name={OUTLINE_NAME}
        positive_prompt_options={filterPositivePrompts}
        agent_type={OUTLINE_NAME}
        negative_prompt_options={negativePrompts}
        isDebugMode={isDebugMode}
      />
    ),
    [ANSWER_REASONING_NAME]: (props) => (
      <LLMNode
        {...props}
        llm_options={llmOptions}
        name={ANSWER_REASONING_NAME}
        positive_prompt_options={filterPositivePrompts}
        agent_type={ANSWER_REASONING_NAME}
        negative_prompt_options={negativePrompts}
        isDebugMode={isDebugMode}
      />
    ),
    [POPULAR_SCIENCE_IMITATING_WRITING_NAME]: (props) => (
      <SearchNode
        {...props}
        llm_options={llmOptions}
        name={POPULAR_SCIENCE_IMITATING_WRITING_NAME}
        database_options={kbOptions}
        positive_prompt_options={filterPositivePrompts}
        agent_type={POPULAR_SCIENCE_IMITATING_WRITING_NAME}
        negative_prompt_options={negativePrompts}
        isDebugMode={isDebugMode}
      />
    ),
    [START_NODE_NAME]: (props) => (
      <StartNode
        {...props}
        llm_options={llmOptions}
        name={START_NODE_NAME}
        database_options={kbOptions}
        positive_prompt_options={filterPositivePrompts}
        agent_type={START_NODE_NAME}
        negative_prompt_options={negativePrompts}
        isDebugMode={isDebugMode}
      />
    ),
    [AGENT_ROUTER_NAME]: (props) => (
      <RouterNode
        {...props}
        llm_options={llmOptions}
      />
    ),
    [TOOL_NODE_NAME]: (props) => (
      <ToolNode
        {...props}
        llm_options={llmOptions}
        name={TOOL_NODE_NAME}
        tools={tools}                  // <--- 将 tools 传给 ToolNode
        mcpServers={mcpServers}
        isDebugMode={isDebugMode}
      />
    ),
    [MCP_RESOURCE_NAME]: (props) => (
      <ResourceNode
        {...props}
        llm_options={llmOptions}
        name={MCP_RESOURCE_NAME}            // <--- 将 tools 传给 ToolNode
        mcpResources={mcpResources}
      />
    ),
    [LLM_NAME]: (props) => (
      <LLMNode
        {...props}
        llm_options={llmOptions}
        name={LLM_NAME}
        positive_prompt_options={filterPositivePrompts}
        agent_type={LLM_NAME}
        negative_prompt_options={negativePrompts}
        isDebugMode={isDebugMode}
      />
    ),
    [INTERRUPTION_NODE_NAME]: (props) => (
      <InterruptionNode
        {...props}
        llm_options={llmOptions}
        name={INTERRUPTION_NODE_NAME}
        positive_prompt_options={filterPositivePrompts}
        agent_type={INTERRUPTION_NODE_NAME}
        negative_prompt_options={negativePrompts}
        isDebugMode={isDebugMode}
      />
    ),
    [FILE_PARSE_NAME]: (props) => (
      <FileParseNode
        {...props}
      />
    ),
    [KNOWLEDGE_BASE_LLM_NAME]: (props) => (
      <SearchNode
        {...props}
        llm_options={llmOptions}
        name={KNOWLEDGE_BASE_LLM_NAME}
        database_options={kbOptions}
        positive_prompt_options={filterPositivePrompts}
        agent_type={KNOWLEDGE_BASE_LLM_NAME}
        negative_prompt_options={negativePrompts}
        mcp_server_options={mcpServers}
        isDebugMode={isDebugMode}
      />
    ),
    [DEEP_RESEARCH_NAME]: (props) => (
      <DeepResearchNode
        {...props}
        llm_options={llmOptions}
        name={DEEP_RESEARCH_NAME}
        database_options={kbOptions}
        mcp_server_options={mcpServers}
        isDebugMode={isDebugMode}
        reflection_prompt={reflection_prompt}
      />
    ),
  });
}
