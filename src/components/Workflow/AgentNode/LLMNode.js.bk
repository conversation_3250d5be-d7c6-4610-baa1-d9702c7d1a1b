// LLMNODE.js
import React, { memo, useCallback, useState, useEffect } from 'react';
import { Handle, useReactFlow, Position } from 'reactflow';
import { Select } from './utils';
import { useNavigate } from 'react-router-dom';
import './CustomNodeWithMultipleHandles.css';
import { PROMPT_ENDPOINT,PROMPT_EDIT_NAME,REFLECTION_NAME } from '../../Configs/Config';
import { fetchData } from '../../Routers/Router';

function LLMNode({ id, data, llm_options, name, positive_prompt_options, agent_type, negative_prompt_options = null, isDebugMode = false }) {
  const { setNodes } = useReactFlow();
  const [result, setResult] = useState('');
  const navigate = useNavigate();

  const filteredPositiveOptions = positive_prompt_options.filter(option => option.task_type !== REFLECTION_NAME);
  const filteredReflectionOptions = positive_prompt_options.filter(option => option.task_type === REFLECTION_NAME);
  const reflectionOptionsWithEmpty = [{ label: null, value: null }, ...filteredReflectionOptions];
  const filteredNegativeOptions = negative_prompt_options;

  const handleChange = useCallback((event, handleId, nodeId) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          const newData = { ...node.data };
          newData.selects = { ...newData.selects, [handleId]: event.target.value };
          return { ...node, data: newData };
        }
        return node;
      })
    );
  }, [setNodes]);

  const handleResultChange = (event) => {
    setResult(event.target.value);
  };

  const handleLabelClick = async (promptType) => {
    const positivePrompt = selects[promptType];
    if (positivePrompt) {
      try {
        const data = await fetchData(`${PROMPT_ENDPOINT}/query_by_name/${positivePrompt}`);
        const promptId = data?.id;
        if (promptId) {
          const currentPath = window.location.pathname;
          navigate(`${PROMPT_EDIT_NAME}/${promptId}`, { state: { from: `${currentPath}` } });
        } else {
          console.error('No prompt ID found for the selected positive prompt.');
        }
      } catch (error) {
        console.error('Failed to fetch the prompt ID:', error);
      }
    }
  };

  useEffect(() => {
    if (isDebugMode) {
      console.log(`Debug mode enabled for node ${id}`);
    }
  }, [isDebugMode, id]);

  const selects = data && data.selects ? data.selects : {};

  return (
    <div className="custom-node">
      <div className="custom-node__header">{name}</div>
      <div className="handle-with-label-input-top-left-1">
        <Handle type="target" position={Position.Left} id="query_input" />
        <span className="handle-label-input-top-left-2">用户输入</span>
      </div>
      <div className="handle-with-label-input-top-left-2">
        <Handle type="target" position={Position.Left} id="llm_input" />
        <span className="handle-label-input-top-left-2">模型输入</span>
      </div>
      <div className="handle-with-label-input-top-left-3">
        <Handle type="target" position={Position.Left} id="attachment_input" />
        <span className="handle-label-input-top-left-3">附件输入</span>
      </div>
      <div className="custom-node__body">
        <div className="llm-select-container">
          <span className="llm-label">大模型</span>
          <Select
            key="llm_name"
            nodeId={id}
            value={selects['llm_name']}
            handleId="llm_name"
            onChange={handleChange}
            options={llm_options}
          />
        </div>
        <div className="positive-prompt-select-container">
          <span
            className="positive-prompt-label"
            onClick={() => handleLabelClick('positive_prompt')}
            style={{ cursor: 'pointer', textDecoration: 'underline' }}
          >
            正向提示词
            <span style={{ fontSize: '8px', color: 'red' }}>
            (点击修改提示词)
            </span>
          </span>
          <Select
            key="positive_prompt"
            nodeId={id}
            value={selects['positive_prompt']}
            handleId="positive_prompt"
            onChange={handleChange}
            options={filteredPositiveOptions}
            className="positive-prompt-select"
          />
        </div>
        <div className="reflection-prompt-select-container">
          <span
            className="reflection-prompt-label"
            onClick={() => handleLabelClick('reflection_prompt')}
            style={{ cursor: 'pointer', textDecoration: 'underline' }}
          >
            反思提示词
            <span style={{ fontSize: '8px', color: 'red' }}>
            (点击修改提示词)
            </span>
          </span>
          <Select
            key="reflection_prompt"
            nodeId={id}
            value={selects['reflection_prompt']}
            handleId="reflection_prompt"
            onChange={handleChange}
            options={reflectionOptionsWithEmpty}
            className="reflection-prompt-select"
          />
        </div>
        {filteredNegativeOptions && filteredNegativeOptions.length > 0 && (
          <div className="negative-prompt-select-container">
            <span className="negative-prompt-label">负向提示词</span>
            <Select
              key="negative_prompt"
              nodeId={id}
              value={selects['negative_prompt']}
              handleId="negative_prompt"
              onChange={handleChange}
              options={filteredNegativeOptions}
              className="negative-prompt-select"
            />
          </div>
        )}
        <div className="result-input-container">
          <span className="result-label">提示词</span>
          <textarea
            value={result}
            onChange={handleResultChange}
            className="result-textarea"
            disabled={!isDebugMode}
            placeholder="Agent输出结果..."
          />
        </div>
      </div>
      <div className="handle-with-label-output-top-right-1">
        <Handle type="source" position={Position.Right} id="llm_output" style={{ background: '#555' }} />
        <span className="handle-label-output-top-right-1">模型输出</span>
      </div>
    </div>
  );
}

export default memo(LLMNode);
