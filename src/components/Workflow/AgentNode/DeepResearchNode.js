// SearchNode.js
import React, { memo, useCallback, useState } from 'react';
import { <PERSON>le, useReactFlow, Position } from 'reactflow';
import { Select } from './utils';
import { Select as AntSelect, Switch } from 'antd';
import styles from './DeepResearchNode.module.css';
import { PROMPT_ENDPOINT,PROMPT_EDIT_NAME,MCP_RESOURCE_NAME,MCP_SERVER_ENDPOINT } from '../../Configs/Config';
import { fetchData } from '../../Routers/Router';
import { useNavigate } from 'react-router-dom';

function DeepResearchNode({
  id, 
  data, 
  llm_options, 
  name, 
  positive_prompt_options, 
  mcp_server_options = [],
  reflection_prompt = []
}) {
  const { setNodes } = useReactFlow();
  const [result, setResult] = useState('');
  const navigate = useNavigate();

  const filteredPositiveOptions = positive_prompt_options;
  const filteredReflectionOptions = reflection_prompt;
  const handleChange = useCallback((event, handleId, nodeId) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          const newData = { ...node.data };
          newData.selects = { ...newData.selects, [handleId]: event.target.value };
          return { ...node, data: newData };
        }
        return node;
      })
    );
  }, [setNodes]);

  // 处理开关变化
  const handleSwitchChange = useCallback((checked, handleId) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === id) {
          const newData = { ...node.data };
          newData.selects = { ...newData.selects, [handleId]: checked };
          return { ...node, data: newData };
        }
        return node;
      })
    );
  }, [setNodes, id]);

  // 处理多选变化
  const handleMultiSelectChange = useCallback((values, handleId) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === id) {
          const newData = { ...node.data };
          newData.selects = { ...newData.selects, [handleId]: values };
          return { ...node, data: newData };
        }
        return node;
      })
    );
  }, [setNodes, id]);

  const handleResultChange = (event) => {
    setResult(event.target.value);
  };

  const handleLabelClick = async () => {
    const positivePrompt = selects['positive_prompt'];
    if (positivePrompt) {
      try {
        const data = await fetchData(`${PROMPT_ENDPOINT}/query_by_name/${positivePrompt}`);
        const promptId = data?.id;
        if (promptId) {
          const currentPath = window.location.pathname;
          navigate(`${PROMPT_EDIT_NAME}/${promptId}`, { state: { from: `${currentPath}` } });
        } else {
          console.error('No prompt ID found for the selected positive prompt.');
        }
      } catch (error) {
        console.error('Failed to fetch the prompt ID:', error);
      }
    }
  };
  const selects = data && data.selects ? data.selects : {};

  return (
    <div className={styles.custom_node}>
      <div className={styles.custom_node_header}>{name}</div>
      <div className={styles.handle_with_label_input_top_left_1}>
        <Handle type="target" position={Position.Left} id="query_input" />
        <span className={styles.handle_label_input_top_left_1}>用户输入</span>
      </div>
      <div className={styles.handle_with_label_input_top_left_2}>
        <Handle type="target" position={Position.Left} id="llm_input" />
        <span className={styles.handle_label_input_top_left_2}>模型输入</span>
      </div>
      <div className={styles.custom_node_body}>
        <div className={styles.llm_select_container}>
          <span className={styles.llm_label}>大模型</span>
          <Select
            key="llm_name"
            nodeId={id}
            value={selects['llm_name']}
            handleId="llm_name"
            onChange={handleChange}
            options={llm_options}
            className={styles.llm_select}
          />
        </div>
        <div className={styles.internet_search_container}>
          <span className={styles.internet_search_label}>互联网搜索工具</span>
          <Switch 
            checked={selects['internet_search_enabled'] || false}
            onChange={(checked) => handleSwitchChange(checked, 'internet_search_enabled')}
            className={styles.internet_search_switch}
          />
        </div>
        <div className={styles.mcp_server_select_container}>
          <span className={styles.mcp_server_label}>MCP服务</span>
          <AntSelect
            mode="multiple"
            placeholder="选择MCP服务"
            value={selects['mcp_server_names'] || []}
            onChange={(values) => handleMultiSelectChange(values, 'mcp_server_names')}
            options={mcp_server_options}
            className="nodrag"
            style={{ 
              width: '100%',
              minHeight: '32px'
            }}
            maxTagCount="responsive"
            maxTagPlaceholder={(omittedValues) => `+${omittedValues.length}...`}
            removeIcon={<span style={{ fontSize: '10px' }}>×</span>}
            suffixIcon={<span style={{ fontSize: '12px' }}>▼</span>}
          />
        </div>
        <div className={styles.positive_prompt_select_container}>
          <span 
            className={styles.positive_prompt_label}
            onClick={handleLabelClick}
            style={{ cursor: 'pointer', textDecoration: 'underline' }}
          >
            正向提示词
            <span style={{ fontSize: '8px', color: 'red' }}>
              (点击修改提示词)
            </span>
          </span>
          <Select 
            key="positive_prompt"
            nodeId={id}
            value={selects['positive_prompt']}
            handleId="positive_prompt"
            onChange={handleChange}
            options={filteredPositiveOptions}
            className={styles.positive_prompt_select}
          />
        </div>
        {filteredReflectionOptions && filteredReflectionOptions.length > 0 && (
          <div className={styles.reflection_prompt_select_container}>
            <span className={styles.reflection_prompt_label}>反思提示词</span>
            <Select
              key="reflection_prompt_name"
              nodeId={id}
              value={selects['reflection_prompt_name']}
              handleId="reflection_prompt_name"
              onChange={handleChange}
              options={filteredReflectionOptions}
              className={styles.reflection_prompt_select}
            />
          </div>
        )}
      </div>
      <div className={styles.handle_with_label_output_top_right_1}>
        <Handle type="source" position={Position.Right} id="attachment_output" style={{ background: '#555' }} />
        <span className={styles.handle_label_output_top_right_1}>附件输出</span>
      </div>
    </div>
  );
}

export default memo(DeepResearchNode);